"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Sidebar from "@/components/dashboard/sidebar"
import Header from "@/components/dashboard/header"

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const [sidebarWidth, setSidebarWidth] = useState(250)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
      setSidebarWidth(window.innerWidth < 1024 ? 0 : 250)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Sidebar />
      <div className="flex flex-col" style={{ marginLeft: isMobile ? 0 : `${sidebarWidth}px` }}>
        <main className="flex-1">{children}</main>
      </div>
    </div>
  )
}
