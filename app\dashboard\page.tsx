"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import {
  BarChart3,
  Network,
  Wallet,
  TrendingUp,
  Shield,
  Copy,
  Eye,
  EyeOff,
  ChevronDown,
  Mail,
  Bell,
  Settings,
  LogOut,
  CheckCircle,
  Boxes
} from "lucide-react"

export default function DashboardPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showLoginId, setShowLoginId] = useState(false)
  const [showServer, setShowServer] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)

  const userEmails = [
    { email: "<EMAIL>", status: "Active", type: "Primary" },
    { email: "<EMAIL>", status: "Verified", type: "Trading" },
    { email: "<EMAIL>", status: "Pending", type: "Alerts" }
  ]

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  const dropdownVariants = {
    hidden: { opacity: 0, y: -10, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.2, ease: "easeOut" }
    },
    exit: {
      opacity: 0,
      y: -10,
      scale: 0.95,
      transition: { duration: 0.2, ease: "easeIn" }
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  return (
    <div className="space-y-10 p-6 bg-gradient-to-b from-[#001a2c] to-[#000c14] min-h-screen">
      {/* Header with User Info */}
      <motion.div
        className="flex items-center justify-between bg-gradient-to-r from-[#002a3c] to-[#001a2c] p-8 rounded-2xl shadow-2xl border border-[#004c66]/30 backdrop-blur-sm"
        variants={fadeInUp}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center space-x-6">
          <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-sky-400 to-blue-600 flex items-center justify-center text-white text-xl font-bold shadow-lg ring-2 ring-[#004c66]/50">
            S
          </div>
          <div>
            <h2 className="text-white text-xl font-bold">Trading Dashboard</h2>
            <p className="text-sky-400 text-sm">Professional Account</p>
          </div>
        </div>
        <div className="flex items-center space-x-6">
          <Button variant="outline" className="w-10 h-10 rounded-xl border-[#004c66] text-sky-400 hover:bg-[#004c66] transition-all duration-300">
            <Bell className="h-5 w-5" />
          </Button>
          <Button variant="outline" className="w-10 h-10 rounded-xl border-[#004c66] text-sky-400 hover:bg-[#004c66] transition-all duration-300">
            <Settings className="h-5 w-5" />
          </Button>
          <div className="relative">
            <Button
              variant="outline"
              className="border-[#004c66] text-white hover:bg-[#004c66] transition-all duration-300 px-4 py-2 h-10 rounded-xl flex items-center space-x-2"
              onClick={() => setShowDropdown(!showDropdown)}
            >
              <Mail className="h-5 w-5 text-sky-400" />
              <span className="text-sm">Accounts</span>
              <ChevronDown className={`h-4 w-4 transition-transform duration-300 ${showDropdown ? 'rotate-180' : ''}`} />
            </Button>

            <AnimatePresence>
              {showDropdown && (
                <motion.div
                  className="absolute right-0 mt-2 w-80 rounded-xl bg-gradient-to-b from-[#002a3c] to-[#001a2c] border border-[#004c66] shadow-2xl z-50"
                  variants={dropdownVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <div className="p-4 border-b border-[#004c66]">
                    <h3 className="text-white font-medium">Connected Accounts</h3>
                    <p className="text-sm text-gray-400">Manage your trading emails</p>
                  </div>
                  <div className="p-2">
                    {userEmails.map((item, index) => (
                      <div
                        key={index}
                        className="p-3 hover:bg-[#003a4c] rounded-lg transition-colors duration-200 cursor-pointer group"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                              item.type === 'Primary' ? 'bg-sky-400/20 text-sky-400' :
                              item.type === 'Trading' ? 'bg-emerald-400/20 text-emerald-400' :
                              'bg-yellow-400/20 text-yellow-400'
                            }`}>
                              <Mail className="h-4 w-4" />
                            </div>
                            <div>
                              <p className="text-white text-sm font-medium">{item.email}</p>
                              <div className="flex items-center space-x-2">
                                <span className="text-xs text-gray-400">{item.type}</span>
                                <span className={`text-xs px-2 py-0.5 rounded-full ${
                                  item.status === 'Active' ? 'bg-sky-400/20 text-sky-400' :
                                  item.status === 'Verified' ? 'bg-emerald-400/20 text-emerald-400' :
                                  'bg-yellow-400/20 text-yellow-400'
                                }`}>
                                  {item.status}
                                </span>
                              </div>
                            </div>
                          </div>
                          <CheckCircle className={`h-4 w-4 ${
                            item.type === 'Primary' ? 'text-sky-400' : 'text-gray-600'
                          }`} />
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="p-2 border-t border-[#004c66]">
                    <Button
                      variant="outline"
                      className="w-full border-[#004c66] text-white hover:bg-[#004c66] transition-all duration-300 space-x-2"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Sign Out</span>
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>

      {/* Account Details */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.1 }}>
        <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] shadow-2xl backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between p-8 border-b border-[#004c66]/30">
            <div>
              <CardTitle className="text-3xl font-bold text-white flex items-center gap-3 mb-2">
                <BarChart3 className="h-8 w-8 text-sky-400" />
                Account Details
              </CardTitle>
              <CardDescription className="text-gray-300 text-lg">
                Manage your trading account credentials
              </CardDescription>
            </div>
            <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20 px-6 py-2.5 text-sm font-medium rounded-xl">
              Pending Verification
            </Badge>
          </CardHeader>
          <CardContent className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
              {/* Platform */}
              <div className="space-y-3 bg-gradient-to-br from-[#001a2c] to-[#002a3c] p-6 rounded-xl border border-[#004c66]/30 hover:border-sky-500/30 transition-all duration-300 group relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-sky-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative">
                  <div className="flex items-center space-x-3 text-gray-300 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-sky-500/20 to-sky-600/20 border border-sky-500/20 group-hover:border-sky-500/40 transition-all duration-300">
                      <BarChart3 className="h-6 w-6 text-sky-400 group-hover:text-sky-300 transition-colors" />
                    </div>
                    <span className="text-sm font-medium">Platform</span>
                  </div>
                  <div className="text-white font-bold text-xl group-hover:text-sky-400 transition-colors">MT4</div>
                </div>
              </div>

              {/* Server */}
              <div className="space-y-3 bg-gradient-to-br from-[#001a2c] to-[#002a3c] p-6 rounded-xl border border-[#004c66]/30 hover:border-emerald-500/30 transition-all duration-300 group relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative">
                  <div className="flex items-center space-x-3 text-gray-300 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-500/20 to-emerald-600/20 border border-emerald-500/20 group-hover:border-emerald-500/40 transition-all duration-300">
                      <Network className="h-6 w-6 text-emerald-400 group-hover:text-emerald-300 transition-colors" />
                    </div>
                    <span className="text-sm font-medium">Server</span>
                  </div>
                  <div className="text-white font-bold text-xl group-hover:text-emerald-400 transition-colors">-</div>
                </div>
              </div>

              {/* Account Size */}
              <div className="space-y-3 bg-gradient-to-br from-[#001a2c] to-[#002a3c] p-6 rounded-xl border border-[#004c66]/30 hover:border-purple-500/30 transition-all duration-300 group relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative">
                  <div className="flex items-center space-x-3 text-gray-300 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-500/20 group-hover:border-purple-500/40 transition-all duration-300">
                      <Wallet className="h-6 w-6 text-purple-400 group-hover:text-purple-300 transition-colors" />
                    </div>
                    <span className="text-sm font-medium">Account Size</span>
                  </div>
                  <div className="text-white font-bold text-xl group-hover:text-purple-400 transition-colors">$3,000</div>
                </div>
              </div>

              {/* Profit Target */}
              <div className="space-y-3 bg-gradient-to-br from-[#001a2c] to-[#002a3c] p-6 rounded-xl border border-[#004c66]/30 hover:border-red-500/30 transition-all duration-300 group relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative">
                  <div className="flex items-center space-x-3 text-gray-300 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-red-500/20 to-red-600/20 border border-red-500/20 group-hover:border-red-500/40 transition-all duration-300">
                      <TrendingUp className="h-6 w-6 text-red-400 group-hover:text-red-300 transition-colors" />
                    </div>
                    <span className="text-sm font-medium">Profit Target</span>
                  </div>
                  <div className="text-white font-bold text-xl group-hover:text-red-400 transition-colors">$NaN</div>
                </div>
              </div>

              {/* Account Type */}
              <div className="space-y-3 bg-gradient-to-br from-[#001a2c] to-[#002a3c] p-6 rounded-xl border border-[#004c66]/30 hover:border-orange-500/30 transition-all duration-300 group relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative">
                  <div className="flex items-center space-x-3 text-gray-300 mb-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500/20 to-orange-600/20 border border-orange-500/20 group-hover:border-orange-500/40 transition-all duration-300">
                      <Shield className="h-6 w-6 text-orange-400 group-hover:text-orange-300 transition-colors" />
                    </div>
                    <span className="text-sm font-medium">Account Type</span>
                  </div>
                  <div className="text-white font-bold text-xl group-hover:text-orange-400 transition-colors">HFT Neo</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Trading Credentials */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
        <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] shadow-2xl backdrop-blur-sm">
          <CardHeader className="p-8 border-b border-[#004c66]/30">
            <CardTitle className="text-3xl font-bold text-white flex items-center gap-3">
              <Boxes className="h-8 w-8 text-sky-400" />
              Trading Credentials
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-8 p-8">
            {/* Login ID */}
            <div className="space-y-3">
              <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                Login ID
                <Badge className="bg-sky-400/10 text-sky-400 border-sky-400/20">Secure</Badge>
              </Label>
              <div className="flex items-center space-x-3">
                <div className="flex-1 bg-gradient-to-r from-[#001a2c] to-[#002a3c] border border-[#004c66] rounded-xl px-4 py-3 shadow-inner">
                  <span className="text-white font-mono text-lg">
                    {showLoginId ? "123456789" : "•••••••••"}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowLoginId(!showLoginId)}
                  className="w-12 h-12 rounded-xl border-[#004c66] text-gray-300 hover:text-white hover:bg-[#004c66] transition-all duration-300"
                >
                  {showLoginId ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard("123456789")}
                  className="w-12 h-12 rounded-xl border-[#004c66] text-gray-300 hover:text-white hover:bg-[#004c66] transition-all duration-300"
                >
                  <Copy className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Password */}
            <div className="space-y-3">
              <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                Password
                <Badge className="bg-emerald-400/10 text-emerald-400 border-emerald-400/20">Protected</Badge>
              </Label>
              <div className="flex items-center space-x-3">
                <div className="flex-1 bg-gradient-to-r from-[#001a2c] to-[#002a3c] border border-[#004c66] rounded-xl px-4 py-3 shadow-inner">
                  <span className="text-white font-mono text-lg">
                    {showPassword ? "MySecurePass123!" : "••••••••••••••••"}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowPassword(!showPassword)}
                  className="w-12 h-12 rounded-xl border-[#004c66] text-gray-300 hover:text-white hover:bg-[#004c66] transition-all duration-300"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard("MySecurePass123!")}
                  className="w-12 h-12 rounded-xl border-[#004c66] text-gray-300 hover:text-white hover:bg-[#004c66] transition-all duration-300"
                >
                  <Copy className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Server */}
            <div className="space-y-3">
              <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                Server
                <Badge className="bg-purple-400/10 text-purple-400 border-purple-400/20">Demo</Badge>
              </Label>
              <div className="flex items-center space-x-3">
                <div className="flex-1 bg-gradient-to-r from-[#001a2c] to-[#002a3c] border border-[#004c66] rounded-xl px-4 py-3 shadow-inner">
                  <span className="text-white font-mono text-lg">
                    {showServer ? "FundedWhales-Demo01" : "•••••••••••••••••••"}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowServer(!showServer)}
                  className="w-12 h-12 rounded-xl border-[#004c66] text-gray-300 hover:text-white hover:bg-[#004c66] transition-all duration-300"
                >
                  {showServer ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard("FundedWhales-Demo01")}
                  className="w-12 h-12 rounded-xl border-[#004c66] text-gray-300 hover:text-white hover:bg-[#004c66] transition-all duration-300"
                >
                  <Copy className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
