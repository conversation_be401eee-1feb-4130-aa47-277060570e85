"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import {
  BarChart3,
  Network,
  Wallet,
  TrendingUp,
  Shield,
  Copy,
  Eye,
  EyeOff,
  ChevronDown,
  Mail,
  Bell,
  Settings,
  LogOut,
  CheckCircle,
  Boxes
} from "lucide-react"

export default function DashboardPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showLoginId, setShowLoginId] = useState(false)
  const [showServer, setShowServer] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)

  const userEmails = [
    { email: "<EMAIL>", status: "Active", type: "Primary" },
    { email: "<EMAIL>", status: "Verified", type: "Trading" },
    { email: "<EMAIL>", status: "Pending", type: "Alerts" }
  ]

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  const dropdownVariants = {
    hidden: { opacity: 0, y: -10, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.2, ease: "easeOut" }
    },
    exit: {
      opacity: 0,
      y: -10,
      scale: 0.95,
      transition: { duration: 0.2, ease: "easeIn" }
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  return (
    <div className="min-h-screen bg-gradient-to-tr from-indigo-900 via-slate-800 to-purple-900 p-8">
      {/* Top Navigation Bar */}
      <motion.div
        className="w-full max-w-7xl mx-auto mb-8"
        variants={fadeInUp}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center justify-between bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-400 to-blue-500 flex items-center justify-center text-white font-bold text-lg">
              S
            </div>
            <div>
              <h1 className="text-white text-2xl font-bold">Trading Dashboard</h1>
              <p className="text-cyan-300 text-sm">Professional Account</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </Button>
            <Button className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <div className="relative">
              <Button
                className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm"
                onClick={() => setShowDropdown(!showDropdown)}
              >
                <Mail className="h-4 w-4 mr-2" />
                Accounts
                <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-300 ${showDropdown ? 'rotate-180' : ''}`} />
              </Button>
              <AnimatePresence>
                {showDropdown && (
                  <motion.div
                    className="absolute right-0 top-full mt-2 w-80 rounded-xl bg-white/10 backdrop-blur-lg border border-white/20 shadow-2xl z-50"
                    variants={dropdownVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <div className="p-4 border-b border-white/20">
                      <h3 className="text-white font-medium">Connected Accounts</h3>
                      <p className="text-sm text-gray-300">Manage your trading emails</p>
                    </div>
                    <div className="p-2">
                      {userEmails.map((item, index) => (
                        <div
                          key={index}
                          className="p-3 hover:bg-white/10 rounded-lg transition-colors duration-200 cursor-pointer group"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                                item.type === 'Primary' ? 'bg-cyan-400/20 text-cyan-400' :
                                item.type === 'Trading' ? 'bg-emerald-400/20 text-emerald-400' :
                                'bg-yellow-400/20 text-yellow-400'
                              }`}>
                                <Mail className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="text-white text-sm font-medium">{item.email}</p>
                                <div className="flex items-center space-x-2">
                                  <span className="text-xs text-gray-400">{item.type}</span>
                                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                                    item.status === 'Active' ? 'bg-cyan-400/20 text-cyan-400' :
                                    item.status === 'Verified' ? 'bg-emerald-400/20 text-emerald-400' :
                                    'bg-yellow-400/20 text-yellow-400'
                                  }`}>
                                    {item.status}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <CheckCircle className={`h-4 w-4 ${
                              item.type === 'Primary' ? 'text-cyan-400' : 'text-gray-600'
                            }`} />
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="p-2 border-t border-white/20">
                      <Button className="w-full bg-red-500/20 hover:bg-red-500/30 text-red-300 border-0">
                        <LogOut className="h-4 w-4 mr-2" />
                        Sign Out
                      </Button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content Grid */}
      <div className="w-full max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">

        {/* Left Column - Account Details */}
        <motion.div
          className="lg:col-span-2 space-y-6"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border-b border-white/20 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                    <BarChart3 className="h-6 w-6 text-cyan-400" />
                    Account Details
                  </CardTitle>
                  <CardDescription className="text-gray-300 mt-2">
                    Manage your trading account credentials
                  </CardDescription>
                </div>
                <Badge className="bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 px-4 py-2 rounded-full">
                  Pending Verification
                </Badge>
              </div>
            </CardHeader>

                  <AnimatePresence>
                    {showDropdown && (
                      <motion.div
                        className="absolute right-0 mt-3 w-96 rounded-2xl bg-gradient-to-b from-[#1a1a2e]/95 to-[#0f0f1a]/95 backdrop-blur-xl border border-white/10 shadow-2xl z-50"
                        variants={dropdownVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                      >
                        <div className="p-6 border-b border-white/10">
                          <h3 className="text-white font-semibold text-lg">Connected Accounts</h3>
                          <p className="text-gray-400 text-sm mt-1">Manage your trading profiles</p>
                        </div>
                        <div className="p-4 space-y-2">
                          {userEmails.map((item, index) => (
                            <div
                              key={index}
                              className="p-4 hover:bg-white/5 rounded-xl transition-all duration-300 cursor-pointer group border border-transparent hover:border-white/10"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                                    item.type === 'Primary' ? 'bg-gradient-to-r from-sky-500/20 to-blue-500/20 border border-sky-500/30' :
                                    item.type === 'Trading' ? 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30' :
                                    'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30'
                                  }`}>
                                    <Mail className="h-5 w-5 text-white" />
                                  </div>
                                  <div>
                                    <p className="text-white font-medium">{item.email}</p>
                                    <div className="flex items-center space-x-3 mt-1">
                                      <span className="text-xs text-gray-400">{item.type}</span>
                                      <Badge className={`text-xs ${
                                        item.status === 'Active' ? 'bg-sky-500/20 text-sky-400 border-sky-500/30' :
                                        item.status === 'Verified' ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30' :
                                        'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                                      }`}>
                                        {item.status}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                                <CheckCircle className={`h-5 w-5 ${
                                  item.type === 'Primary' ? 'text-sky-400' : 'text-gray-600'
                                }`} />
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="p-4 border-t border-white/10">
                          <Button className="w-full bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30 text-red-300 hover:from-red-500/30 hover:to-pink-500/30 transition-all duration-300">
                            <LogOut className="h-4 w-4 mr-2" />
                            Sign Out
                          </Button>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

            <CardContent className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {/* Platform */}
                <div className="bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-xl p-4 border border-cyan-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-cyan-500/20">
                      <BarChart3 className="h-5 w-5 text-cyan-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Platform</span>
                  </div>
                  <div className="text-white font-bold text-lg">MT4</div>
                </div>

                {/* Server */}
                <div className="bg-gradient-to-br from-emerald-500/10 to-green-500/10 rounded-xl p-4 border border-emerald-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-emerald-500/20">
                      <Network className="h-5 w-5 text-emerald-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Server</span>
                  </div>
                  <div className="text-white font-bold text-lg">-</div>
                </div>

                {/* Account Size */}
                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl p-4 border border-purple-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-purple-500/20">
                      <Wallet className="h-5 w-5 text-purple-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Account Size</span>
                  </div>
                  <div className="text-white font-bold text-lg">$3,000</div>
                </div>

                {/* Profit Target */}
                <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-xl p-4 border border-orange-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-orange-500/20">
                      <TrendingUp className="h-5 w-5 text-orange-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Profit Target</span>
                  </div>
                  <div className="text-white font-bold text-lg">$NaN</div>
                </div>

                {/* Account Type */}
                <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-xl p-4 border border-yellow-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-yellow-500/20">
                      <Shield className="h-5 w-5 text-yellow-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Account Type</span>
                  </div>
                  <div className="text-white font-bold text-lg">HFT Neo</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Trading Credentials */}
          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-b border-white/20 p-6">
              <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <Boxes className="h-6 w-6 text-purple-400" />
                Trading Credentials
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              {/* Login ID */}
              <div className="space-y-3">
                <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                  Login ID
                  <Badge className="bg-cyan-400/20 text-cyan-400 border border-cyan-400/30 text-xs">Secure</Badge>
                </Label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-white/5 border border-white/20 rounded-xl px-4 py-3 backdrop-blur-sm">
                    <span className="text-white font-mono text-lg">
                      {showLoginId ? "123456789" : "•••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowLoginId(!showLoginId)}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    {showLoginId ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("123456789")}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {/* Password */}
              <div className="space-y-3">
                <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                  Password
                  <Badge className="bg-emerald-400/20 text-emerald-400 border border-emerald-400/30 text-xs">Protected</Badge>
                </Label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-white/5 border border-white/20 rounded-xl px-4 py-3 backdrop-blur-sm">
                    <span className="text-white font-mono text-lg">
                      {showPassword ? "MySecurePass123!" : "••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowPassword(!showPassword)}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("MySecurePass123!")}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Server */}
              <div className="space-y-3">
                <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                  Server
                  <Badge className="bg-purple-400/20 text-purple-400 border border-purple-400/30 text-xs">Demo</Badge>
                </Label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-white/5 border border-white/20 rounded-xl px-4 py-3 backdrop-blur-sm">
                    <span className="text-white font-mono text-lg">
                      {showServer ? "FundedWhales-Demo01" : "•••••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowServer(!showServer)}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    {showServer ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("FundedWhales-Demo01")}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Right Column - Quick Stats */}
        <motion.div
          className="space-y-6"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-b border-white/20 p-6">
              <CardTitle className="text-xl font-bold text-white">Quick Overview</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Balance</span>
                <span className="text-white font-bold">$2,850.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Equity</span>
                <span className="text-white font-bold">$2,850.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Free Margin</span>
                <span className="text-white font-bold">$2,850.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Margin Level</span>
                <span className="text-emerald-400 font-bold">∞%</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-orange-500/20 to-red-500/20 border-b border-white/20 p-6">
              <CardTitle className="text-xl font-bold text-white">Trading Rules</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Max Daily Loss</span>
                <span className="text-red-400 font-bold">5%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Max Total Loss</span>
                <span className="text-red-400 font-bold">10%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Profit Target</span>
                <span className="text-emerald-400 font-bold">10%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Min Trading Days</span>
                <span className="text-cyan-400 font-bold">5 days</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

        {/* Recent Activity */}
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.3 }}
        >
          <Card className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#1a1a2e]/80 to-[#0f0f1a]/80 backdrop-blur-xl border border-white/10 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-blue-500/5 to-purple-500/5" />
            <CardHeader className="relative p-8 border-b border-white/10">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-white via-emerald-200 to-blue-200 bg-clip-text text-transparent flex items-center gap-3">
                <Activity className="h-8 w-8 text-emerald-400" />
                Recent Activity
              </CardTitle>
              <CardDescription className="text-gray-300 text-lg">
                Your latest trading actions and updates
              </CardDescription>
            </CardHeader>
            <CardContent className="relative p-8">
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-white/5 to-transparent border border-white/10 hover:border-white/20 transition-all duration-300 group"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        activity.type === 'profit' ? 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30' :
                        activity.type === 'loss' ? 'bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30' :
                        'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30'
                      }`}>
                        {activity.type === 'profit' ? <TrendingUp className="h-6 w-6 text-emerald-400" /> :
                         activity.type === 'loss' ? <ArrowDownRight className="h-6 w-6 text-red-400" /> :
                         <CheckCircle className="h-6 w-6 text-blue-400" />}
                      </div>
                      <div>
                        <p className="text-white font-semibold">{activity.action}</p>
                        <div className="flex items-center space-x-2 text-sm text-gray-400">
                          <span>{activity.symbol}</span>
                          <span>•</span>
                          <span>{activity.time}</span>
                        </div>
                      </div>
                    </div>
                    <div className={`text-lg font-bold ${
                      activity.type === 'profit' ? 'text-emerald-400' :
                      activity.type === 'loss' ? 'text-red-400' :
                      'text-blue-400'
                    }`}>
                      {activity.profit}
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Trading Credentials */}
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.4 }}
        >
          <Card className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#1a1a2e]/80 to-[#0f0f1a]/80 backdrop-blur-xl border border-white/10 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-red-500/5" />
            <CardHeader className="relative p-8 border-b border-white/10">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent flex items-center gap-3">
                <Lock className="h-8 w-8 text-purple-400" />
                Trading Credentials
              </CardTitle>
              <CardDescription className="text-gray-300 text-lg">
                Secure access to your trading platform
              </CardDescription>
            </CardHeader>
            <CardContent className="relative p-8 space-y-8">
              {/* Login ID */}
              <div className="space-y-4">
                <Label className="text-gray-300 text-base font-semibold flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-sky-500/20 to-blue-500/20 border border-sky-500/30">
                    <Users className="h-4 w-4 text-sky-400" />
                  </div>
                  Login ID
                  <Badge className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-400 border-sky-500/30">
                    <Lock className="h-3 w-3 mr-1" />
                    Secure
                  </Badge>
                </Label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative overflow-hidden rounded-2xl bg-gradient-to-r from-[#1a1a2e]/60 to-[#0f0f1a]/60 border border-white/10 p-4 backdrop-blur-sm">
                    <div className="absolute inset-0 bg-gradient-to-r from-sky-500/5 to-transparent" />
                    <span className="relative text-white font-mono text-xl tracking-wider">
                      {showLoginId ? "123456789" : "•••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowLoginId(!showLoginId)}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-sky-500/20 to-blue-500/20 border border-sky-500/30 text-sky-300 hover:from-sky-500/30 hover:to-blue-500/30 transition-all duration-300"
                  >
                    {showLoginId ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("123456789")}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30 text-emerald-300 hover:from-emerald-500/30 hover:to-green-500/30 transition-all duration-300"
                  >
                    <Copy className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Password */}
              <div className="space-y-4">
                <Label className="text-gray-300 text-base font-semibold flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30">
                    <Lock className="h-4 w-4 text-emerald-400" />
                  </div>
                  Password
                  <Badge className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 text-emerald-400 border-emerald-500/30">
                    <Shield className="h-3 w-3 mr-1" />
                    Protected
                  </Badge>
                </Label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative overflow-hidden rounded-2xl bg-gradient-to-r from-[#1a1a2e]/60 to-[#0f0f1a]/60 border border-white/10 p-4 backdrop-blur-sm">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-transparent" />
                    <span className="relative text-white font-mono text-xl tracking-wider">
                      {showPassword ? "MySecurePass123!" : "••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowPassword(!showPassword)}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30 text-emerald-300 hover:from-emerald-500/30 hover:to-green-500/30 transition-all duration-300"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("MySecurePass123!")}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30 transition-all duration-300"
                  >
                    <Copy className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Server */}
              <div className="space-y-4">
                <Label className="text-gray-300 text-base font-semibold flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30">
                    <Network className="h-4 w-4 text-purple-400" />
                  </div>
                  Server
                  <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-400 border-purple-500/30">
                    <Globe className="h-3 w-3 mr-1" />
                    Demo
                  </Badge>
                </Label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative overflow-hidden rounded-2xl bg-gradient-to-r from-[#1a1a2e]/60 to-[#0f0f1a]/60 border border-white/10 p-4 backdrop-blur-sm">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-transparent" />
                    <span className="relative text-white font-mono text-xl tracking-wider">
                      {showServer ? "FundedWhales-Demo01" : "•••••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowServer(!showServer)}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30 transition-all duration-300"
                  >
                    {showServer ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("FundedWhales-Demo01")}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 text-orange-300 hover:from-orange-500/30 hover:to-red-500/30 transition-all duration-300"
                  >
                    <Copy className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
