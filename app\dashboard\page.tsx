"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import {
  BarChart3,
  Network,
  Wallet,
  TrendingUp,
  Shield,
  Copy,
  Eye,
  EyeOff,
  ChevronDown,
  Mail,
  Bell,
  Settings,
  LogOut,
  CheckCircle,
  Boxes
} from "lucide-react"

export default function DashboardPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showLoginId, setShowLoginId] = useState(false)
  const [showServer, setShowServer] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)

  const userEmails = [
    { email: "<EMAIL>", status: "Active", type: "Primary" },
    { email: "<EMAIL>", status: "Verified", type: "Trading" },
    { email: "<EMAIL>", status: "Pending", type: "Alerts" }
  ]

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  const dropdownVariants = {
    hidden: { opacity: 0, y: -10, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.2, ease: "easeOut" }
    },
    exit: {
      opacity: 0,
      y: -10,
      scale: 0.95,
      transition: { duration: 0.2, ease: "easeIn" }
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="min-h-screen bg-gradient-to-tr from-indigo-900 via-slate-800 to-purple-900 p-8">
      {/* Top Navigation Bar */}
      <motion.div
        className="w-full max-w-7xl mx-auto mb-8"
        variants={fadeInUp}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center justify-between bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-400 to-blue-500 flex items-center justify-center text-white font-bold text-lg">
              S
            </div>
            <div>
              <h1 className="text-white text-2xl font-bold">Trading Dashboard</h1>
              <p className="text-cyan-300 text-sm">Professional Account</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </Button>
            <Button className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <div className="relative">
              <Button
                className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm"
                onClick={() => setShowDropdown(!showDropdown)}
              >
                <Mail className="h-4 w-4 mr-2" />
                Accounts
                <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-300 ${showDropdown ? 'rotate-180' : ''}`} />
              </Button>
              <AnimatePresence>
                {showDropdown && (
                  <motion.div
                    className="absolute right-0 top-full mt-2 w-80 rounded-xl bg-white/10 backdrop-blur-lg border border-white/20 shadow-2xl z-50"
                    variants={dropdownVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <div className="p-4 border-b border-white/20">
                      <h3 className="text-white font-medium">Connected Accounts</h3>
                      <p className="text-sm text-gray-300">Manage your trading emails</p>
                    </div>
                    <div className="p-2">
                      {userEmails.map((item, index) => (
                        <div
                          key={index}
                          className="p-3 hover:bg-white/10 rounded-lg transition-colors duration-200 cursor-pointer group"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                                item.type === 'Primary' ? 'bg-cyan-400/20 text-cyan-400' :
                                item.type === 'Trading' ? 'bg-emerald-400/20 text-emerald-400' :
                                'bg-yellow-400/20 text-yellow-400'
                              }`}>
                                <Mail className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="text-white text-sm font-medium">{item.email}</p>
                                <div className="flex items-center space-x-2">
                                  <span className="text-xs text-gray-400">{item.type}</span>
                                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                                    item.status === 'Active' ? 'bg-cyan-400/20 text-cyan-400' :
                                    item.status === 'Verified' ? 'bg-emerald-400/20 text-emerald-400' :
                                    'bg-yellow-400/20 text-yellow-400'
                                  }`}>
                                    {item.status}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <CheckCircle className={`h-4 w-4 ${
                              item.type === 'Primary' ? 'text-cyan-400' : 'text-gray-600'
                            }`} />
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="p-2 border-t border-white/20">
                      <Button className="w-full bg-red-500/20 hover:bg-red-500/30 text-red-300 border-0">
                        <LogOut className="h-4 w-4 mr-2" />
                        Sign Out
                      </Button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content Grid */}
      <div className="w-full max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        {/* Left Column - Account Details */}
        <motion.div 
          className="lg:col-span-2 space-y-6"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.1 }}
        >
          {/* Account Details Card */}
          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border-b border-white/20 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                    <BarChart3 className="h-6 w-6 text-cyan-400" />
                    Account Details
                  </CardTitle>
                  <CardDescription className="text-gray-300 mt-2">
                    Manage your trading account credentials
                  </CardDescription>
                </div>
                <Badge className="bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 px-4 py-2 rounded-full">
                  Pending Verification
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {/* Platform */}
                <div className="bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-xl p-4 border border-cyan-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-cyan-500/20">
                      <BarChart3 className="h-5 w-5 text-cyan-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Platform</span>
                  </div>
                  <div className="text-white font-bold text-lg">MT4</div>
                </div>

                {/* Server */}
                <div className="bg-gradient-to-br from-emerald-500/10 to-green-500/10 rounded-xl p-4 border border-emerald-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-emerald-500/20">
                      <Network className="h-5 w-5 text-emerald-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Server</span>
                  </div>
                  <div className="text-white font-bold text-lg">-</div>
                </div>

                {/* Account Size */}
                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl p-4 border border-purple-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-purple-500/20">
                      <Wallet className="h-5 w-5 text-purple-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Account Size</span>
                  </div>
                  <div className="text-white font-bold text-lg">$3,000</div>
                </div>

                {/* Profit Target */}
                <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-xl p-4 border border-orange-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-orange-500/20">
                      <TrendingUp className="h-5 w-5 text-orange-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Profit Target</span>
                  </div>
                  <div className="text-white font-bold text-lg">$NaN</div>
                </div>

                {/* Account Type */}
                <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-xl p-4 border border-yellow-500/20">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 rounded-lg bg-yellow-500/20">
                      <Shield className="h-5 w-5 text-yellow-400" />
                    </div>
                    <span className="text-gray-300 text-sm font-medium">Account Type</span>
                  </div>
                  <div className="text-white font-bold text-lg">HFT Neo</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Trading Credentials */}
          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-b border-white/20 p-6">
              <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <Boxes className="h-6 w-6 text-purple-400" />
                Trading Credentials
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              {/* Login ID */}
              <div className="space-y-3">
                <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                  Login ID
                  <Badge className="bg-cyan-400/20 text-cyan-400 border border-cyan-400/30 text-xs">Secure</Badge>
                </Label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-white/5 border border-white/20 rounded-xl px-4 py-3 backdrop-blur-sm">
                    <span className="text-white font-mono text-lg">
                      {showLoginId ? "123456789" : "•••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowLoginId(!showLoginId)}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    {showLoginId ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("123456789")}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Password */}
              <div className="space-y-3">
                <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                  Password
                  <Badge className="bg-emerald-400/20 text-emerald-400 border border-emerald-400/30 text-xs">Protected</Badge>
                </Label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-white/5 border border-white/20 rounded-xl px-4 py-3 backdrop-blur-sm">
                    <span className="text-white font-mono text-lg">
                      {showPassword ? "MySecurePass123!" : "••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowPassword(!showPassword)}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("MySecurePass123!")}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Server */}
              <div className="space-y-3">
                <Label className="text-gray-300 text-sm font-medium flex items-center gap-2">
                  Server
                  <Badge className="bg-purple-400/20 text-purple-400 border border-purple-400/30 text-xs">Demo</Badge>
                </Label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1 bg-white/5 border border-white/20 rounded-xl px-4 py-3 backdrop-blur-sm">
                    <span className="text-white font-mono text-lg">
                      {showServer ? "FundedWhales-Demo01" : "•••••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowServer(!showServer)}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    {showServer ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("FundedWhales-Demo01")}
                    className="bg-white/10 hover:bg-white/20 text-white border-0 backdrop-blur-sm"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Right Column - Quick Stats */}
        <motion.div
          className="space-y-6"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-b border-white/20 p-6">
              <CardTitle className="text-xl font-bold text-white">Quick Overview</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Balance</span>
                <span className="text-white font-bold">$2,850.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Equity</span>
                <span className="text-white font-bold">$2,850.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Free Margin</span>
                <span className="text-white font-bold">$2,850.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Margin Level</span>
                <span className="text-emerald-400 font-bold">∞%</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-orange-500/20 to-red-500/20 border-b border-white/20 p-6">
              <CardTitle className="text-xl font-bold text-white">Trading Rules</CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Max Daily Loss</span>
                <span className="text-red-400 font-bold">5%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Max Total Loss</span>
                <span className="text-red-400 font-bold">10%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Profit Target</span>
                <span className="text-emerald-400 font-bold">10%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Min Trading Days</span>
                <span className="text-cyan-400 font-bold">5 days</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
