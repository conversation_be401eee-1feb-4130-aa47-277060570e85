"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import {
  BarChart3,
  Network,
  Wallet,
  TrendingUp,
  Shield,
  Copy,
  Eye,
  EyeOff,
  ChevronDown,
  Mail,
  Bell,
  Settings,
  LogOut,
  CheckCircle,
  Boxes,
  Activity,
  Zap,
  Globe,
  Lock,
  Star,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  Users,
  Calendar,
  Clock,
  Sparkles,
  Crown,
  Diamond
} from "lucide-react"

export default function DashboardPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showLoginId, setShowLoginId] = useState(false)
  const [showServer, setShowServer] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [accountProgress, setAccountProgress] = useState(65)

  const userEmails = [
    { email: "<EMAIL>", status: "Active", type: "Primary" },
    { email: "<EMAIL>", status: "Verified", type: "Trading" },
    { email: "<EMAIL>", status: "Pending", type: "Alerts" }
  ]

  const tradingStats = [
    { label: "Total Trades", value: "247", change: "+12%", trend: "up", icon: Activity },
    { label: "Win Rate", value: "68.4%", change: "+2.1%", trend: "up", icon: TrendingUp },
    { label: "Profit Factor", value: "1.85", change: "-0.05", trend: "down", icon: BarChart3 },
    { label: "Max Drawdown", value: "4.2%", change: "-0.8%", trend: "up", icon: Shield }
  ]

  const recentActivity = [
    { action: "Trade Executed", symbol: "EURUSD", profit: "+$125.50", time: "2 min ago", type: "profit" },
    { action: "Position Closed", symbol: "GBPJPY", profit: "-$45.20", time: "15 min ago", type: "loss" },
    { action: "Trade Executed", symbol: "USDJPY", profit: "+$89.75", time: "32 min ago", type: "profit" },
    { action: "Account Verified", symbol: "KYC", profit: "Completed", time: "1 hour ago", type: "info" }
  ]

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  const dropdownVariants = {
    hidden: { opacity: 0, y: -10, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.2, ease: "easeOut" }
    },
    exit: {
      opacity: 0,
      y: -10,
      scale: 0.95,
      transition: { duration: 0.2, ease: "easeIn" }
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#0f0f1a] to-[#1a1a2e] relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-sky-500/10 to-purple-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      <div className="relative z-10 p-6 space-y-8">
        {/* Premium Header */}
        <motion.div
          className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-[#1a1a2e]/80 via-[#16213e]/80 to-[#0f3460]/80 backdrop-blur-xl border border-white/10 shadow-2xl"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-sky-500/5 via-purple-500/5 to-emerald-500/5" />
          <div className="relative p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-sky-400 via-purple-500 to-emerald-400 flex items-center justify-center text-white text-2xl font-bold shadow-2xl">
                    <Crown className="h-8 w-8" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                    <Sparkles className="h-3 w-3 text-white" />
                  </div>
                </div>
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-white via-sky-200 to-purple-200 bg-clip-text text-transparent">
                      Elite Trading Hub
                    </h1>
                    <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30 px-3 py-1">
                      <Diamond className="h-3 w-3 mr-1" />
                      Premium
                    </Badge>
                  </div>
                  <p className="text-gray-300 text-lg">Professional Trader • Level 5</p>
                  <div className="flex items-center space-x-4 mt-2">
                    <div className="flex items-center space-x-2 text-sm text-gray-400">
                      <Clock className="h-4 w-4" />
                      <span>{currentTime.toLocaleTimeString()}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-400">
                      <Globe className="h-4 w-4" />
                      <span>New York Session</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Button className="bg-gradient-to-r from-sky-500/20 to-purple-500/20 border border-sky-500/30 text-sky-300 hover:from-sky-500/30 hover:to-purple-500/30 transition-all duration-300 backdrop-blur-sm">
                  <Bell className="h-4 w-4 mr-2" />
                  <span className="relative">
                    Alerts
                    <span className="absolute -top-2 -right-2 w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                  </span>
                </Button>
                <Button className="bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-500/30 text-emerald-300 hover:from-emerald-500/30 hover:to-blue-500/30 transition-all duration-300 backdrop-blur-sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Button>
                <div className="relative">
                  <Button
                    className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30 transition-all duration-300 backdrop-blur-sm"
                    onClick={() => setShowDropdown(!showDropdown)}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    <span>Accounts</span>
                    <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-300 ${showDropdown ? 'rotate-180' : ''}`} />
                  </Button>

                  <AnimatePresence>
                    {showDropdown && (
                      <motion.div
                        className="absolute right-0 mt-3 w-96 rounded-2xl bg-gradient-to-b from-[#1a1a2e]/95 to-[#0f0f1a]/95 backdrop-blur-xl border border-white/10 shadow-2xl z-50"
                        variants={dropdownVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                      >
                        <div className="p-6 border-b border-white/10">
                          <h3 className="text-white font-semibold text-lg">Connected Accounts</h3>
                          <p className="text-gray-400 text-sm mt-1">Manage your trading profiles</p>
                        </div>
                        <div className="p-4 space-y-2">
                          {userEmails.map((item, index) => (
                            <div
                              key={index}
                              className="p-4 hover:bg-white/5 rounded-xl transition-all duration-300 cursor-pointer group border border-transparent hover:border-white/10"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                                    item.type === 'Primary' ? 'bg-gradient-to-r from-sky-500/20 to-blue-500/20 border border-sky-500/30' :
                                    item.type === 'Trading' ? 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30' :
                                    'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30'
                                  }`}>
                                    <Mail className="h-5 w-5 text-white" />
                                  </div>
                                  <div>
                                    <p className="text-white font-medium">{item.email}</p>
                                    <div className="flex items-center space-x-3 mt-1">
                                      <span className="text-xs text-gray-400">{item.type}</span>
                                      <Badge className={`text-xs ${
                                        item.status === 'Active' ? 'bg-sky-500/20 text-sky-400 border-sky-500/30' :
                                        item.status === 'Verified' ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30' :
                                        'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                                      }`}>
                                        {item.status}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                                <CheckCircle className={`h-5 w-5 ${
                                  item.type === 'Primary' ? 'text-sky-400' : 'text-gray-600'
                                }`} />
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="p-4 border-t border-white/10">
                          <Button className="w-full bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30 text-red-300 hover:from-red-500/30 hover:to-pink-500/30 transition-all duration-300">
                            <LogOut className="h-4 w-4 mr-2" />
                            Sign Out
                          </Button>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Trading Statistics */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.1 }}
        >
          {tradingStats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <motion.div
                key={index}
                className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-[#1a1a2e]/60 to-[#0f0f1a]/60 backdrop-blur-xl border border-white/10 p-6 hover:border-white/20 transition-all duration-500 group"
                whileHover={{ y: -5, scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-sky-500/5 via-purple-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="relative">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${
                      index === 0 ? 'from-sky-500/20 to-blue-500/20 border border-sky-500/30' :
                      index === 1 ? 'from-emerald-500/20 to-green-500/20 border border-emerald-500/30' :
                      index === 2 ? 'from-purple-500/20 to-pink-500/20 border border-purple-500/30' :
                      'from-orange-500/20 to-red-500/20 border border-orange-500/30'
                    }`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className={`flex items-center space-x-1 text-sm ${
                      stat.trend === 'up' ? 'text-emerald-400' : 'text-red-400'
                    }`}>
                      {stat.trend === 'up' ? <ArrowUpRight className="h-4 w-4" /> : <ArrowDownRight className="h-4 w-4" />}
                      <span>{stat.change}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold text-white">{stat.value}</h3>
                    <p className="text-gray-400 text-sm">{stat.label}</p>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Account Progress */}
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2 }}
        >
          <Card className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#1a1a2e]/80 to-[#0f0f1a]/80 backdrop-blur-xl border border-white/10 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-sky-500/5 via-purple-500/5 to-emerald-500/5" />
            <CardHeader className="relative p-8 border-b border-white/10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-3xl font-bold bg-gradient-to-r from-white via-sky-200 to-purple-200 bg-clip-text text-transparent flex items-center gap-3 mb-2">
                    <Zap className="h-8 w-8 text-yellow-400" />
                    Account Progress
                  </CardTitle>
                  <CardDescription className="text-gray-300 text-lg">
                    Your journey to funded trading
                  </CardDescription>
                </div>
                <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30 px-6 py-3 text-base font-semibold rounded-xl">
                  <Star className="h-4 w-4 mr-2" />
                  Phase 1
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative p-8">
              <div className="space-y-8">
                {/* Progress Bar */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300 font-medium">Challenge Progress</span>
                    <span className="text-white font-bold">{accountProgress}%</span>
                  </div>
                  <div className="relative">
                    <Progress value={accountProgress} className="h-3 bg-white/10 rounded-full overflow-hidden" />
                    <div className="absolute inset-0 bg-gradient-to-r from-sky-500 via-purple-500 to-emerald-500 rounded-full opacity-80" style={{ width: `${accountProgress}%` }} />
                  </div>
                  <div className="flex justify-between text-sm text-gray-400">
                    <span>Start</span>
                    <span>Current: $1,950</span>
                    <span>Target: $3,300</span>
                  </div>
                </div>

                {/* Account Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
                  {/* Platform */}
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-500/20 p-6 hover:border-sky-500/40 transition-all duration-500 group">
                    <div className="absolute inset-0 bg-gradient-to-r from-sky-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-r from-sky-500/20 to-blue-500/20 border border-sky-500/30">
                          <BarChart3 className="h-6 w-6 text-sky-400" />
                        </div>
                        <Badge className="bg-sky-500/20 text-sky-400 border-sky-500/30 text-xs">Active</Badge>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-white">MT4</h3>
                        <p className="text-gray-400 text-sm">Platform</p>
                      </div>
                    </div>
                  </div>

                  {/* Server */}
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-500/10 to-green-500/10 border border-emerald-500/20 p-6 hover:border-emerald-500/40 transition-all duration-500 group">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30">
                          <Network className="h-6 w-6 text-emerald-400" />
                        </div>
                        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs">Pending</Badge>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-white">-</h3>
                        <p className="text-gray-400 text-sm">Server</p>
                      </div>
                    </div>
                  </div>

                  {/* Account Size */}
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 p-6 hover:border-purple-500/40 transition-all duration-500 group">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30">
                          <Wallet className="h-6 w-6 text-purple-400" />
                        </div>
                        <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30 text-xs">Funded</Badge>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-white">$3,000</h3>
                        <p className="text-gray-400 text-sm">Account Size</p>
                      </div>
                    </div>
                  </div>

                  {/* Profit Target */}
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 p-6 hover:border-orange-500/40 transition-all duration-500 group">
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30">
                          <TrendingUp className="h-6 w-6 text-orange-400" />
                        </div>
                        <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30 text-xs">On Track</Badge>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-white">$300</h3>
                        <p className="text-gray-400 text-sm">Profit Target</p>
                      </div>
                    </div>
                  </div>

                  {/* Account Type */}
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 p-6 hover:border-yellow-500/40 transition-all duration-500 group">
                    <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="relative">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 rounded-xl bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30">
                          <Shield className="h-6 w-6 text-yellow-400" />
                        </div>
                        <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30 text-xs">
                          <Crown className="h-3 w-3 mr-1" />
                          Elite
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-white">HFT Neo</h3>
                        <p className="text-gray-400 text-sm">Account Type</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.3 }}
        >
          <Card className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#1a1a2e]/80 to-[#0f0f1a]/80 backdrop-blur-xl border border-white/10 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-blue-500/5 to-purple-500/5" />
            <CardHeader className="relative p-8 border-b border-white/10">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-white via-emerald-200 to-blue-200 bg-clip-text text-transparent flex items-center gap-3">
                <Activity className="h-8 w-8 text-emerald-400" />
                Recent Activity
              </CardTitle>
              <CardDescription className="text-gray-300 text-lg">
                Your latest trading actions and updates
              </CardDescription>
            </CardHeader>
            <CardContent className="relative p-8">
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-white/5 to-transparent border border-white/10 hover:border-white/20 transition-all duration-300 group"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        activity.type === 'profit' ? 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30' :
                        activity.type === 'loss' ? 'bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30' :
                        'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30'
                      }`}>
                        {activity.type === 'profit' ? <TrendingUp className="h-6 w-6 text-emerald-400" /> :
                         activity.type === 'loss' ? <ArrowDownRight className="h-6 w-6 text-red-400" /> :
                         <CheckCircle className="h-6 w-6 text-blue-400" />}
                      </div>
                      <div>
                        <p className="text-white font-semibold">{activity.action}</p>
                        <div className="flex items-center space-x-2 text-sm text-gray-400">
                          <span>{activity.symbol}</span>
                          <span>•</span>
                          <span>{activity.time}</span>
                        </div>
                      </div>
                    </div>
                    <div className={`text-lg font-bold ${
                      activity.type === 'profit' ? 'text-emerald-400' :
                      activity.type === 'loss' ? 'text-red-400' :
                      'text-blue-400'
                    }`}>
                      {activity.profit}
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Trading Credentials */}
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.4 }}
        >
          <Card className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[#1a1a2e]/80 to-[#0f0f1a]/80 backdrop-blur-xl border border-white/10 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-red-500/5" />
            <CardHeader className="relative p-8 border-b border-white/10">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent flex items-center gap-3">
                <Lock className="h-8 w-8 text-purple-400" />
                Trading Credentials
              </CardTitle>
              <CardDescription className="text-gray-300 text-lg">
                Secure access to your trading platform
              </CardDescription>
            </CardHeader>
            <CardContent className="relative p-8 space-y-8">
              {/* Login ID */}
              <div className="space-y-4">
                <Label className="text-gray-300 text-base font-semibold flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-sky-500/20 to-blue-500/20 border border-sky-500/30">
                    <Users className="h-4 w-4 text-sky-400" />
                  </div>
                  Login ID
                  <Badge className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 text-sky-400 border-sky-500/30">
                    <Lock className="h-3 w-3 mr-1" />
                    Secure
                  </Badge>
                </Label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative overflow-hidden rounded-2xl bg-gradient-to-r from-[#1a1a2e]/60 to-[#0f0f1a]/60 border border-white/10 p-4 backdrop-blur-sm">
                    <div className="absolute inset-0 bg-gradient-to-r from-sky-500/5 to-transparent" />
                    <span className="relative text-white font-mono text-xl tracking-wider">
                      {showLoginId ? "123456789" : "•••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowLoginId(!showLoginId)}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-sky-500/20 to-blue-500/20 border border-sky-500/30 text-sky-300 hover:from-sky-500/30 hover:to-blue-500/30 transition-all duration-300"
                  >
                    {showLoginId ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("123456789")}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30 text-emerald-300 hover:from-emerald-500/30 hover:to-green-500/30 transition-all duration-300"
                  >
                    <Copy className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Password */}
              <div className="space-y-4">
                <Label className="text-gray-300 text-base font-semibold flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30">
                    <Lock className="h-4 w-4 text-emerald-400" />
                  </div>
                  Password
                  <Badge className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 text-emerald-400 border-emerald-500/30">
                    <Shield className="h-3 w-3 mr-1" />
                    Protected
                  </Badge>
                </Label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative overflow-hidden rounded-2xl bg-gradient-to-r from-[#1a1a2e]/60 to-[#0f0f1a]/60 border border-white/10 p-4 backdrop-blur-sm">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-transparent" />
                    <span className="relative text-white font-mono text-xl tracking-wider">
                      {showPassword ? "MySecurePass123!" : "••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowPassword(!showPassword)}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30 text-emerald-300 hover:from-emerald-500/30 hover:to-green-500/30 transition-all duration-300"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("MySecurePass123!")}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30 transition-all duration-300"
                  >
                    <Copy className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Server */}
              <div className="space-y-4">
                <Label className="text-gray-300 text-base font-semibold flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30">
                    <Network className="h-4 w-4 text-purple-400" />
                  </div>
                  Server
                  <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-400 border-purple-500/30">
                    <Globe className="h-3 w-3 mr-1" />
                    Demo
                  </Badge>
                </Label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative overflow-hidden rounded-2xl bg-gradient-to-r from-[#1a1a2e]/60 to-[#0f0f1a]/60 border border-white/10 p-4 backdrop-blur-sm">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-transparent" />
                    <span className="relative text-white font-mono text-xl tracking-wider">
                      {showServer ? "FundedWhales-Demo01" : "•••••••••••••••••••"}
                    </span>
                  </div>
                  <Button
                    onClick={() => setShowServer(!showServer)}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30 transition-all duration-300"
                  >
                    {showServer ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </Button>
                  <Button
                    onClick={() => copyToClipboard("FundedWhales-Demo01")}
                    className="w-14 h-14 rounded-2xl bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 text-orange-300 hover:from-orange-500/30 hover:to-red-500/30 transition-all duration-300"
                  >
                    <Copy className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
