"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Upload, QrCode, CreditCard, CheckCircle, DollarSign, Shield, Clock, Award } from "lucide-react"

export default function BuyAccountPage() {
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    challengeType: "",
    platform: "",
    size: "",
    paymentMethod: "",
    txid: "",
    proofImage: null as File | null,
  })
  
  const [showPayment, setShowPayment] = useState(false)
  const [orderPlaced, setOrderPlaced] = useState(false)
  const [selectedPrice, setSelectedPrice] = useState<number>(0)

  const challengeTypes = [
    { 
      value: "hft-pro", 
      label: "HFT PRO",
      description: "High-Frequency Trading Challenge",
      features: ["Unlimited trading time", "Advanced algorithms allowed", "API trading supported"]
    },
    { 
      value: "phase1", 
      label: "Phase 1",
      description: "Initial Evaluation Phase",
      features: ["14 days trading period", "10% profit target", "8% maximum drawdown"]
    },
    { 
      value: "phase2", 
      label: "Phase 2",
      description: "Advanced Evaluation Phase",
      features: ["30 days trading period", "5% profit target", "4% maximum drawdown"]
    },
  ]

  const platforms = [
    { value: "mt4", label: "MetaTrader 4", icon: "🔷" },
    { value: "mt5", label: "MetaTrader 5", icon: "🔶" },
  ]

  const sizes = [
    { value: "1000", label: "$1,000", price: 49, features: ["1:100 Leverage", "0.01 Minimum Lot"] },
    { value: "5000", label: "$5,000", price: 99, features: ["1:100 Leverage", "0.05 Minimum Lot"] },
    { value: "10000", label: "$10,000", price: 149, features: ["1:100 Leverage", "0.1 Minimum Lot"] },
    { value: "25000", label: "$25,000", price: 249, features: ["1:100 Leverage", "0.25 Minimum Lot"] },
    { value: "50000", label: "$50,000", price: 399, features: ["1:100 Leverage", "0.5 Minimum Lot"] },
  ]

  const paymentMethods = [
    { value: "usdt-trc20", label: "USDT (TRC20)", qr: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIxNjAiIGZpbGw9IiNmZmYiLz48L3N2Zz4=" },
    { value: "usdt-erc20", label: "USDT (ERC20)", qr: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPjxyZWN0IHg9IjMwIiB5PSIzMCIgd2lkdGg9IjE0MCIgaGVpZ2h0PSIxNDAiIGZpbGw9IiNmZmYiLz48L3N2Zz4=" },
    { value: "bitcoin", label: "Bitcoin", qr: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPjxyZWN0IHg9IjQwIiB5PSI0MCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxMjAiIGZpbGw9IiNmZmYiLz48L3N2Zz4=" },
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (field === "paymentMethod" && value) {
      setShowPayment(true)
    }
    if (field === "size") {
      const selectedSize = sizes.find(s => s.value === value)
      setSelectedPrice(selectedSize?.price || 0)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormData(prev => ({ ...prev, proofImage: file }))
    }
  }

  const handlePlaceOrder = () => {
    if (!formData.username || !formData.email || !formData.challengeType || 
        !formData.platform || !formData.size || !formData.paymentMethod || 
        !formData.txid || !formData.proofImage) {
      alert("Please fill in all required fields")
      return
    }
    setOrderPlaced(true)
  }

  const selectedPaymentMethod = paymentMethods.find(pm => pm.value === formData.paymentMethod)
  const selectedChallengeType = challengeTypes.find(ct => ct.value === formData.challengeType)

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  if (orderPlaced) {
    return (
      <motion.div 
        className="flex items-center justify-center min-h-[60vh]"
        variants={fadeInUp}
        initial="hidden"
        animate="visible"
      >
        <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] max-w-md w-full shadow-2xl">
          <CardContent className="text-center p-8">
            <CheckCircle className="h-20 w-20 text-emerald-500 mx-auto mb-6" />
            <h2 className="text-3xl font-bold text-white mb-4">Order Placed Successfully!</h2>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Your order has been submitted and is being processed. You will receive an email confirmation shortly.
            </p>
            <Button 
              onClick={() => setOrderPlaced(false)}
              className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg"
            >
              Place Another Order
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <div className="space-y-8 p-6">
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] shadow-2xl">
          <CardHeader className="p-8 border-b border-[#003a4c]">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-3xl font-bold text-white flex items-center mb-2">
                  <CreditCard className="mr-3 h-8 w-8 text-sky-400" />
                  Buy Trading Account
                </CardTitle>
                <CardDescription className="text-gray-300 text-lg">
                  Start your trading journey with our professional accounts
                </CardDescription>
              </div>
              {selectedPrice > 0 && (
                <div className="text-right">
                  <div className="text-2xl font-bold text-white">${selectedPrice}</div>
                  <div className="text-sky-400 text-sm">Selected Plan Price</div>
                </div>
              )}
            </div>
          </CardHeader>

          <CardContent className="p-8 space-y-8">
            {/* Challenge Type Selection */}
            <div className="space-y-4">
              <Label className="text-xl font-semibold text-white">Select Challenge Type *</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {challengeTypes.map((type) => (
                  <div
                    key={type.value}
                    className={`p-6 rounded-xl border transition-all duration-300 cursor-pointer ${
                      formData.challengeType === type.value
                        ? "bg-[#003a4c] border-sky-500"
                        : "bg-[#001a2c] border-[#003a4c] hover:border-sky-500/50"
                    }`}
                    onClick={() => handleInputChange("challengeType", type.value)}
                  >
                    <h3 className="text-lg font-bold text-white mb-2">{type.label}</h3>
                    <p className="text-gray-400 text-sm mb-4">{type.description}</p>
                    <ul className="space-y-2">
                      {type.features.map((feature, index) => (
                        <li key={index} className="text-gray-300 text-sm flex items-center">
                          <Shield className="h-4 w-4 text-sky-400 mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>

            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="username" className="text-white font-medium">Username *</Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) => handleInputChange("username", e.target.value)}
                  className="bg-[#001a2c] border-[#003a4c] text-white h-12"
                  placeholder="Enter your username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white font-medium">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="bg-[#001a2c] border-[#003a4c] text-white h-12"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Account Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-white font-medium">Trading Platform *</Label>
                <Select value={formData.platform} onValueChange={(value) => handleInputChange("platform", value)}>
                  <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white h-12">
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#001a2c] border-[#003a4c]">
                    {platforms.map((platform) => (
                      <SelectItem key={platform.value} value={platform.value} className="text-white hover:bg-[#003a4c]">
                        <span className="mr-2">{platform.icon}</span>
                        {platform.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-white font-medium">Account Size *</Label>
                <Select value={formData.size} onValueChange={(value) => handleInputChange("size", value)}>
                  <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white h-12">
                    <SelectValue placeholder="Select size" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#001a2c] border-[#003a4c]">
                    {sizes.map((size) => (
                      <SelectItem key={size.value} value={size.value} className="text-white hover:bg-[#003a4c]">
                        <div className="flex justify-between items-center w-full">
                          <span>{size.label}</span>
                          <span className="text-sky-400">${size.price}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Payment Method */}
            <div className="space-y-2">
              <Label className="text-white font-medium">Payment Method *</Label>
              <Select value={formData.paymentMethod} onValueChange={(value) => handleInputChange("paymentMethod", value)}>
                <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white h-12">
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent className="bg-[#001a2c] border-[#003a4c]">
                  {paymentMethods.map((method) => (
                    <SelectItem key={method.value} value={method.value} className="text-white hover:bg-[#003a4c]">
                      {method.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Payment Details */}
            {showPayment && selectedPaymentMethod && (
              <motion.div 
                className="space-y-6 p-6 bg-gradient-to-br from-[#001a2c] to-[#002a3c] rounded-xl border border-[#004c66] shadow-lg"
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
              >
                <h3 className="text-xl font-bold text-white flex items-center">
                  <QrCode className="mr-3 h-6 w-6 text-sky-400" />
                  Payment Details - {selectedPaymentMethod.label}
                </h3>
                
                <div className="flex flex-col md:flex-row gap-8">
                  <div className="flex-1">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-sky-500/20 via-purple-500/20 to-teal-500/20 rounded-xl blur-xl"></div>
                      <div className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] p-8 rounded-xl border border-[#004c66]/50 shadow-2xl backdrop-blur-sm relative">
                        <div className="bg-white p-6 rounded-xl shadow-lg">
                          <img 
                            src={selectedPaymentMethod.qr} 
                            alt="QR Code"
                            className="w-full h-auto"
                          />
                        </div>
                        <div className="mt-6 space-y-3">
                          <div className="flex items-center justify-between p-4 bg-[#001a2c] rounded-lg border border-[#004c66]/30">
                            <span className="text-gray-400">Amount to Pay</span>
                            <div className="text-2xl font-bold bg-gradient-to-r from-sky-400 via-blue-500 to-purple-500 text-transparent bg-clip-text">
                              ${selectedPrice}
                            </div>
                          </div>
                          <div className="flex items-center justify-center space-x-2 text-sm text-gray-400">
                            <QrCode className="h-4 w-4 text-sky-400" />
                            <span>Scan QR code to complete payment</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex-1 space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="txid" className="text-white font-medium">Transaction ID (TXID) *</Label>
                      <Input
                        id="txid"
                        value={formData.txid}
                        onChange={(e) => handleInputChange("txid", e.target.value)}
                        className="bg-[#001a2c] border-[#003a4c] text-white h-12"
                        placeholder="Enter transaction ID"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="proof" className="text-white font-medium">Payment Proof *</Label>
                      <div className="flex items-center space-x-3">
                        <Input
                          id="proof"
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          className="bg-[#001a2c] border-[#003a4c] text-white file:bg-sky-500 file:text-white file:border-0 file:rounded-lg file:px-4 file:py-2 file:mr-4 file:hover:bg-sky-600 file:transition-colors"
                        />
                        <Upload className="h-6 w-6 text-sky-400" />
                      </div>
                      {formData.proofImage && (
                        <p className="text-sm text-sky-400 mt-2 flex items-center">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {formData.proofImage.name}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Submit Button */}
            <Button 
              onClick={handlePlaceOrder}
              className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white text-lg font-medium py-6 rounded-xl shadow-lg transition-all duration-300"
              disabled={!formData.username || !formData.email || !formData.challengeType || 
                       !formData.platform || !formData.size || !formData.paymentMethod || 
                       !formData.txid || !formData.proofImage}
            >
              Place Order {selectedPrice > 0 && `- $${selectedPrice}`}
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
